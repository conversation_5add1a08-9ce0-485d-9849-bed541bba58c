import React, { useRef, useState, useEffect, Suspense, useMemo, forwardRef, useImperativeHandle, useCallback } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import {
  OrbitControls,
  Stars,
  useTexture,
  Sphere,
  Html
} from '@react-three/drei';
import { useNavigate } from 'react-router-dom';
import * as THREE from 'three';
import '../styles/WelcomePage.css';

// 添加CSS动画样式
const tooltipStyles = `
  @keyframes tooltipFadeIn {
    0% {
      opacity: 0;
      transform: translate(0, -100%) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translate(0, -100%) scale(1);
    }
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = tooltipStyles;
  document.head.appendChild(styleSheet);
}

// 轨道粒子组件
function OrbitingParticles() {
  const particlesRef = useRef();

  // 创建轨道粒子
  const particleCount = 50;
  const positions = useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      const radius = 3.2 + Math.random() * 0.8;
      const theta = (i / particleCount) * Math.PI * 2;
      const phi = Math.random() * Math.PI * 0.4 - Math.PI * 0.2; // 限制在赤道附近

      pos[i * 3] = radius * Math.cos(theta) * Math.cos(phi);
      pos[i * 3 + 1] = radius * Math.sin(phi);
      pos[i * 3 + 2] = radius * Math.sin(theta) * Math.cos(phi);
    }
    return pos;
  }, []);

  // 动画效果
  useFrame(({ clock }) => {
    if (particlesRef.current) {
      const time = clock.getElapsedTime();
      const positions = particlesRef.current.geometry.attributes.position.array;

      for (let i = 0; i < particleCount; i++) {
        const radius = 3.2 + Math.sin(time * 0.5 + i * 0.1) * 0.3;
        const theta = (i / particleCount) * Math.PI * 2 + time * 0.2;
        const phi = Math.sin(time * 0.3 + i * 0.05) * 0.2;

        positions[i * 3] = radius * Math.cos(theta) * Math.cos(phi);
        positions[i * 3 + 1] = radius * Math.sin(phi);
        positions[i * 3 + 2] = radius * Math.sin(theta) * Math.cos(phi);
      }

      particlesRef.current.geometry.attributes.position.needsUpdate = true;
    }
  });

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.04}
        color="#00e5ff"
        transparent
        opacity={0.8}
        sizeAttenuation={false}
      />
    </points>
  );
}

// 现代科技感立体浮雕世界地图组件 - 带国家边界版
const ModernTechEarth = forwardRef((props, ref) => {
  const [landData, setLandData] = useState(null);
  const [countriesData, setCountriesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hoveredCountry, setHoveredCountry] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const [showTooltip, setShowTooltip] = useState(false);
  const groupRef = useRef();
  const raycaster = useRef(new THREE.Raycaster());
  const mouse = useRef(new THREE.Vector2());

  // 获取当前语言设置
  const currentLang = localStorage.getItem('preferredLanguage') || 'en';

  // 国家名称映射（英文到中文）
  const countryNameMap = {
    'United States of America': '美国',
    'United States': '美国',
    'China': '中国',
    'Russia': '俄罗斯',
    'Canada': '加拿大',
    'Brazil': '巴西',
    'Australia': '澳大利亚',
    'India': '印度',
    'Argentina': '阿根廷',
    'Kazakhstan': '哈萨克斯坦',
    'Algeria': '阿尔及利亚',
    'Democratic Republic of the Congo': '刚果民主共和国',
    'Saudi Arabia': '沙特阿拉伯',
    'Mexico': '墨西哥',
    'Indonesia': '印度尼西亚',
    'Sudan': '苏丹',
    'Libya': '利比亚',
    'Iran': '伊朗',
    'Mongolia': '蒙古',
    'Peru': '秘鲁',
    'Chad': '乍得',
    'Niger': '尼日尔',
    'Angola': '安哥拉',
    'Mali': '马里',
    'South Africa': '南非',
    'Colombia': '哥伦比亚',
    'Ethiopia': '埃塞俄比亚',
    'Bolivia': '玻利维亚',
    'Mauritania': '毛里塔尼亚',
    'Egypt': '埃及',
    'Tanzania': '坦桑尼亚',
    'Nigeria': '尼日利亚',
    'Venezuela': '委内瑞拉',
    'Namibia': '纳米比亚',
    'Mozambique': '莫桑比克',
    'Pakistan': '巴基斯坦',
    'Turkey': '土耳其',
    'Chile': '智利',
    'Zambia': '赞比亚',
    'Myanmar': '缅甸',
    'Afghanistan': '阿富汗',
    'Somalia': '索马里',
    'Central African Republic': '中非共和国',
    'South Sudan': '南苏丹',
    'Ukraine': '乌克兰',
    'Madagascar': '马达加斯加',
    'Botswana': '博茨瓦纳',
    'Kenya': '肯尼亚',
    'France': '法国',
    'Yemen': '也门',
    'Thailand': '泰国',
    'Spain': '西班牙',
    'Turkmenistan': '土库曼斯坦',
    'Cameroon': '喀麦隆',
    'Papua New Guinea': '巴布亚新几内亚',
    'Sweden': '瑞典',
    'Uzbekistan': '乌兹别克斯坦',
    'Morocco': '摩洛哥',
    'Iraq': '伊拉克',
    'Paraguay': '巴拉圭',
    'Zimbabwe': '津巴布韦',
    'Norway': '挪威',
    'Japan': '日本',
    'Germany': '德国',
    'Republic of the Congo': '刚果共和国',
    'Finland': '芬兰',
    'Vietnam': '越南',
    'Malaysia': '马来西亚',
    'Poland': '波兰',
    'Oman': '阿曼',
    'Italy': '意大利',
    'Philippines': '菲律宾',
    'Ecuador': '厄瓜多尔',
    'Burkina Faso': '布基纳法索',
    'New Zealand': '新西兰',
    'Gabon': '加蓬',
    'Guinea': '几内亚',
    'United Kingdom': '英国',
    'Uganda': '乌干达',
    'Ghana': '加纳',
    'Romania': '罗马尼亚',
    'Laos': '老挝',
    'Guyana': '圭亚那',
    'Belarus': '白俄罗斯',
    'Kyrgyzstan': '吉尔吉斯斯坦',
    'Senegal': '塞内加尔',
    'Syria': '叙利亚',
    'Cambodia': '柬埔寨',
    'Uruguay': '乌拉圭',
    'Tunisia': '突尼斯',
    'Suriname': '苏里南',
    'Bangladesh': '孟加拉国',
    'Nepal': '尼泊尔',
    'Tajikistan': '塔吉克斯坦',
    'Greece': '希腊',
    'Nicaragua': '尼加拉瓜',
    'North Korea': '朝鲜',
    'Malawi': '马拉维',
    'Eritrea': '厄立特里亚',
    'Benin': '贝宁',
    'Honduras': '洪都拉斯',
    'Liberia': '利比里亚',
    'Bulgaria': '保加利亚',
    'Cuba': '古巴',
    'Guatemala': '危地马拉',
    'Iceland': '冰岛',
    'South Korea': '韩国',
    'Hungary': '匈牙利',
    'Jordan': '约旦',
    'Portugal': '葡萄牙',
    'Serbia': '塞尔维亚',
    'Azerbaijan': '阿塞拜疆',
    'Austria': '奥地利',
    'United Arab Emirates': '阿联酋',
    'Czech Republic': '捷克',
    'Panama': '巴拿马',
    'Sierra Leone': '塞拉利昂',
    'Ireland': '爱尔兰',
    'Georgia': '格鲁吉亚',
    'Sri Lanka': '斯里兰卡',
    'Lithuania': '立陶宛',
    'Latvia': '拉脱维亚',
    'Togo': '多哥',
    'Croatia': '克罗地亚',
    'Bosnia and Herzegovina': '波黑',
    'Costa Rica': '哥斯达黎加',
    'Slovakia': '斯洛伐克',
    'Dominican Republic': '多米尼加',
    'Estonia': '爱沙尼亚',
    'Denmark': '丹麦',
    'Netherlands': '荷兰',
    'Switzerland': '瑞士',
    'Bhutan': '不丹',
    'Guinea-Bissau': '几内亚比绍',
    'Moldova': '摩尔多瓦',
    'Belgium': '比利时',
    'Lesotho': '莱索托',
    'Armenia': '亚美尼亚',
    'Albania': '阿尔巴尼亚',
    'Solomon Islands': '所罗门群岛',
    'Equatorial Guinea': '赤道几内亚',
    'Burundi': '布隆迪',
    'Haiti': '海地',
    'Rwanda': '卢旺达',
    'Macedonia': '马其顿',
    'Djibouti': '吉布提',
    'Belize': '伯利兹',
    'El Salvador': '萨尔瓦多',
    'Israel': '以色列',
    'Slovenia': '斯洛文尼亚',
    'Fiji': '斐济',
    'Kuwait': '科威特',
    'Swaziland': '斯威士兰',
    'Timor-Leste': '东帝汶',
    'Montenegro': '黑山',
    'Bahrain': '巴林',
    'Vanuatu': '瓦努阿图',
    'Qatar': '卡塔尔',
    'Gambia': '冈比亚',
    'Jamaica': '牙买加',
    'Lebanon': '黎巴嫩',
    'Cyprus': '塞浦路斯',
    'Brunei': '文莱',
    'Trinidad and Tobago': '特立尼达和多巴哥',
    'Cape Verde': '佛得角',
    'Samoa': '萨摩亚',
    'Luxembourg': '卢森堡',
    'Comoros': '科摩罗',
    'Mauritius': '毛里求斯',
    'São Tomé and Príncipe': '圣多美和普林西比',
    'Kiribati': '基里巴斯',
    'Dominica': '多米尼克',
    'Tonga': '汤加',
    'Micronesia': '密克罗尼西亚',
    'Singapore': '新加坡',
    'Bahamas': '巴哈马',
    'Palau': '帕劳',
    'Seychelles': '塞舌尔',
    'Andorra': '安道尔',
    'Antigua and Barbuda': '安提瓜和巴布达',
    'Barbados': '巴巴多斯',
    'Saint Vincent and the Grenadines': '圣文森特和格林纳丁斯',
    'Grenada': '格林纳达',
    'Malta': '马耳他',
    'Maldives': '马尔代夫',
    'Saint Kitts and Nevis': '圣基茨和尼维斯',
    'Marshall Islands': '马绍尔群岛',
    'Liechtenstein': '列支敦士登',
    'San Marino': '圣马力诺',
    'Tuvalu': '图瓦卢',
    'Nauru': '瑙鲁',
    'Monaco': '摩纳哥',
    'Vatican City': '梵蒂冈'
  };

  // 获取国家名称（根据当前语言）
  const getCountryName = (englishName) => {
    if (currentLang === 'zh') {
      return countryNameMap[englishName] || englishName;
    }
    return englishName;
  };

  // 加载GeoJSON数据
  useEffect(() => {
    const loadData = async () => {
      try {
        const [landResponse, countriesResponse] = await Promise.all([
          fetch('/welcomeEarthLandData/ne_50m_land.json'),
          fetch('/welcomeEarthLandData/ne_50m_admin_0_countries.json')
        ]);

        const landData = await landResponse.json();
        const countriesData = await countriesResponse.json();

        console.log('Land data loaded:', landData.features?.length, 'features');
        console.log('Countries data loaded:', countriesData.features?.length, 'countries');

        setLandData(landData);
        setCountriesData(countriesData);
        setLoading(false);
      } catch (error) {
        console.error('Error loading GeoJSON data:', error);
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // 创建立体投影 - 将地理坐标转换为3D坐标
  const projection = useCallback((lon, lat, elevation = 0) => {
    const x = (lon / 180) * 8; // 经度映射到 -8 到 8
    const y = (lat / 90) * 4;   // 纬度映射到 -4 到 4

    // 根据纬度添加地形变化
    const baseElevation = Math.sin((lat / 90) * Math.PI) * 0.08;
    const randomVariation = (Math.random() - 0.5) * 0.04;
    const totalElevation = elevation + baseElevation + randomVariation;

    return [x, y, totalElevation];
  }, []);

  // 创建立体大陆地形
  const continentMeshes = useMemo(() => {
    if (!landData) return [];

    const meshes = [];

    landData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const shape = new THREE.Shape();
          let firstPoint = true;

          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y] = projection(coord[0], coord[1]);

              if (firstPoint) {
                shape.moveTo(x, y);
                firstPoint = false;
              } else {
                shape.lineTo(x, y);
              }
            }
          });

          if (shape.curves.length > 2) {
            // 大陆立体高度
            const continentElevation = 0.15 + Math.random() * 0.08;

            // 挤出设置
            const extrudeSettings = {
              depth: continentElevation,
              bevelEnabled: true,
              bevelThickness: 0.02,
              bevelSize: 0.015,
              bevelOffset: 0,
              bevelSegments: 2
            };

            meshes.push({
              shape,
              extrudeSettings,
              material: {
                color: '#2c5a6b',
                metalness: 0.3,
                roughness: 0.6,
                opacity: 0.95,
                transparent: true,
                emissive: '#1a3a4a',
                emissiveIntensity: 0.1
              }
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${meshes.length} continent relief meshes`);
    return meshes;
  }, [landData, projection]);

  // 创建国家边界线
  const countryBorders = useMemo(() => {
    if (!countriesData) return [];

    const borders = [];

    countriesData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const countryName = feature.properties?.NAME || feature.properties?.name || `Country ${featureIndex}`;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const points = [];
          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y, z] = projection(coord[0], coord[1], 0.25); // 边界线稍微高一点
              points.push(new THREE.Vector3(x, y, z));
            }
          });

          if (points.length > 2) {
            // 闭合边界线
            points.push(points[0]);

            borders.push({
              points,
              countryName,
              featureIndex
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${borders.length} country border lines`);
    return borders;
  }, [countriesData, projection]);

  // 创建国家检测网格 - 用于提高hover检测精度
  const countryMeshes = useMemo(() => {
    if (!countriesData) return [];

    const meshes = [];

    countriesData.features.forEach((feature, featureIndex) => {
      if (!feature.geometry || !feature.geometry.coordinates) return;

      const countryName = feature.properties?.NAME || feature.properties?.name || `Country ${featureIndex}`;

      const processCoordinates = (coords, depth = 0) => {
        if (depth === 0 && Array.isArray(coords[0]) && Array.isArray(coords[0][0])) {
          coords.forEach(ring => processCoordinates(ring, depth + 1));
        } else if (depth === 1 && Array.isArray(coords[0]) && !Array.isArray(coords[0][0])) {
          if (coords.length < 3) return;

          const shape = new THREE.Shape();
          let firstPoint = true;

          coords.forEach((coord, index) => {
            if (coord && coord.length >= 2) {
              const [x, y] = projection(coord[0], coord[1]);

              if (firstPoint) {
                shape.moveTo(x, y);
                firstPoint = false;
              } else {
                shape.lineTo(x, y);
              }
            }
          });

          if (shape.curves.length > 2) {
            meshes.push({
              shape,
              countryName,
              featureIndex,
              geometry: feature.geometry
            });
          }
        } else {
          coords.forEach(subCoords => processCoordinates(subCoords, depth + 1));
        }
      };

      if (feature.geometry.type === 'Polygon') {
        processCoordinates(feature.geometry.coordinates, 0);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(polygon => {
          processCoordinates(polygon, 0);
        });
      }
    });

    console.log(`Generated ${meshes.length} country detection meshes`);
    console.log('Sample country mesh:', meshes[0]?.countryName);
    return meshes;
  }, [countriesData, projection]);

  // 增强的鼠标移动处理函数 - 提高检测灵敏度
  const handleMouseMove = useCallback((event) => {
    if (!groupRef.current) return;

    // 获取画布元素
    const canvas = event.target;
    const rect = canvas.getBoundingClientRect();

    // 计算鼠标在画布中的标准化坐标 (-1 到 1)
    const mouseX = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    const mouseY = -((event.clientY - rect.top) / rect.height) * 2 + 1;

    // 更新鼠标位置（添加平滑处理）
    mouse.current.x = mouseX;
    mouse.current.y = mouseY;

    // 计算工具提示位置，确保不会超出屏幕边界
    const tooltipOffset = 15;
    const tooltipWidth = 200; // 估计的工具提示宽度
    const tooltipHeight = 40; // 估计的工具提示高度

    let tooltipX = event.clientX + tooltipOffset;
    let tooltipY = event.clientY - tooltipOffset;

    // 检查右边界
    if (tooltipX + tooltipWidth > window.innerWidth) {
      tooltipX = event.clientX - tooltipWidth - tooltipOffset;
    }

    // 检查上边界
    if (tooltipY - tooltipHeight < 0) {
      tooltipY = event.clientY + tooltipOffset + tooltipHeight;
    }

    // 检查左边界
    if (tooltipX < 0) {
      tooltipX = tooltipOffset;
    }

    // 检查下边界
    if (tooltipY + tooltipHeight > window.innerHeight) {
      tooltipY = window.innerHeight - tooltipHeight - tooltipOffset;
    }

    // 更新工具提示位置
    setTooltipPosition({
      x: tooltipX,
      y: tooltipY
    });
  }, []);

  // 鼠标离开处理函数
  const handleMouseLeave = useCallback(() => {
    setHoveredCountry(null);
    setShowTooltip(false);
  }, []);

  // 优化的射线检测系统 - 多重检测方法和增强灵敏度
  useFrame(({ camera }) => {
    if (!groupRef.current || !countriesData || !countryMeshes.length) return;

    // 获取所有可交互的对象
    const intersectableObjects = [];
    const countryDetectors = [];

    groupRef.current.traverse((child) => {
      if (child.isMesh && child.geometry) {
        // 分离国家检测器和普通网格
        if (child.userData && child.userData.isCountryDetector) {
          countryDetectors.push(child);
        } else {
          intersectableObjects.push(child);
        }
      }
    });

    let foundCountry = null;

    // 多点射线检测 - 提高灵敏度
    const detectionPoints = [
      { x: mouse.current.x, y: mouse.current.y }, // 中心点
      { x: mouse.current.x + 0.01, y: mouse.current.y }, // 右
      { x: mouse.current.x - 0.01, y: mouse.current.y }, // 左
      { x: mouse.current.x, y: mouse.current.y + 0.01 }, // 上
      { x: mouse.current.x, y: mouse.current.y - 0.01 }, // 下
      { x: mouse.current.x + 0.007, y: mouse.current.y + 0.007 }, // 右上
      { x: mouse.current.x - 0.007, y: mouse.current.y + 0.007 }, // 左上
      { x: mouse.current.x + 0.007, y: mouse.current.y - 0.007 }, // 右下
      { x: mouse.current.x - 0.007, y: mouse.current.y - 0.007 }  // 左下
    ];

    // 优先检测国家检测器（最精确）
    if (countryDetectors.length > 0) {
      // 调试信息 - 只在第一次显示
      if (!window.detectorCountLogged) {
        console.log('Country detectors available:', countryDetectors.length);
        window.detectorCountLogged = true;
      }
      for (const point of detectionPoints) {
        raycaster.current.setFromCamera(point, camera);
        const countryIntersects = raycaster.current.intersectObjects(countryDetectors, false);
        if (countryIntersects.length > 0) {
          const countryIntersect = countryIntersects[0];
          if (countryIntersect.object.userData && countryIntersect.object.userData.countryName) {
            foundCountry = countryIntersect.object.userData.countryName;
            // 调试信息
            if (foundCountry !== hoveredCountry) {
              console.log('Country detected via detector mesh:', foundCountry);
            }
            break;
          }
        }
      }
    }

    // 如果国家检测器没有检测到，使用传统方法
    if (!foundCountry && intersectableObjects.length > 0) {
      for (const point of detectionPoints) {
        raycaster.current.setFromCamera(point, camera);
        const intersects = raycaster.current.intersectObjects(intersectableObjects, false);

        if (intersects.length > 0) {
          const intersect = intersects[0];
          const intersectPoint = intersect.point;

          // 方法1: 直接使用3D坐标进行国家检测（更精确）
          foundCountry = findCountryByPoint(intersectPoint);

          // 方法2: 如果方法1失败，使用传统的地理坐标转换
          if (!foundCountry) {
            const lon = (intersectPoint.x / 8) * 180;
            const lat = (intersectPoint.y / 4) * 90;
            foundCountry = findCountryByGeoCoordinates(lon, lat);
          }

          // 方法3: 如果仍然失败，使用扩展搜索半径
          if (!foundCountry) {
            foundCountry = findCountryByExpandedSearch(intersectPoint);
          }

          if (foundCountry) break;
        }
      }
    }

    // 更新状态
    if (foundCountry && foundCountry !== hoveredCountry) {
      setHoveredCountry(foundCountry);
      setShowTooltip(true);
    } else if (!foundCountry && hoveredCountry) {
      setHoveredCountry(null);
      setShowTooltip(false);
    }
  });

  // 方法1: 通过3D点直接查找国家（最精确）
  const findCountryByPoint = useCallback((point) => {
    const tolerance = 0.1; // 容差范围

    for (const meshData of countryMeshes) {
      // 检查点是否在国家形状的边界框内
      const shape = meshData.shape;
      if (shape && shape.curves && shape.curves.length > 0) {
        // 简单的边界框检测
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;

        shape.curves.forEach(curve => {
          if (curve.v1) {
            minX = Math.min(minX, curve.v1.x);
            maxX = Math.max(maxX, curve.v1.x);
            minY = Math.min(minY, curve.v1.y);
            maxY = Math.max(maxY, curve.v1.y);
          }
          if (curve.v2) {
            minX = Math.min(minX, curve.v2.x);
            maxX = Math.max(maxX, curve.v2.x);
            minY = Math.min(minY, curve.v2.y);
            maxY = Math.max(maxY, curve.v2.y);
          }
        });

        // 检查点是否在边界框内（带容差）
        if (point.x >= minX - tolerance && point.x <= maxX + tolerance &&
            point.y >= minY - tolerance && point.y <= maxY + tolerance) {

          // 进一步检查是否在实际形状内
          const lon = (point.x / 8) * 180;
          const lat = (point.y / 4) * 90;

          if (isPointInCountry(lon, lat, meshData.geometry)) {
            return meshData.countryName;
          }
        }
      }
    }
    return null;
  }, [countryMeshes]);

  // 方法2: 传统地理坐标查找
  const findCountryByGeoCoordinates = useCallback((lon, lat) => {
    for (const feature of countriesData.features) {
      if (feature.geometry && isPointInCountry(lon, lat, feature.geometry)) {
        return feature.properties?.NAME || feature.properties?.name || 'Unknown';
      }
    }
    return null;
  }, [countriesData]);

  // 方法3: 扩展搜索半径查找
  const findCountryByExpandedSearch = useCallback((point) => {
    const searchRadius = 0.2; // 搜索半径
    const searchSteps = 8; // 搜索步数

    for (let i = 1; i <= searchSteps; i++) {
      const radius = (searchRadius / searchSteps) * i;

      // 在圆周上检查多个点
      for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 4) {
        const testX = point.x + Math.cos(angle) * radius;
        const testY = point.y + Math.sin(angle) * radius;

        const lon = (testX / 8) * 180;
        const lat = (testY / 4) * 90;

        const country = findCountryByGeoCoordinates(lon, lat);
        if (country) {
          return country;
        }
      }
    }
    return null;
  }, [findCountryByGeoCoordinates]);

  // 简单的点在多边形内检测函数
  const isPointInCountry = (lon, lat, geometry) => {
    if (geometry.type === 'Polygon') {
      return isPointInPolygon(lon, lat, geometry.coordinates[0]);
    } else if (geometry.type === 'MultiPolygon') {
      for (const polygon of geometry.coordinates) {
        if (isPointInPolygon(lon, lat, polygon[0])) {
          return true;
        }
      }
    }
    return false;
  };

  // 射线投射算法检测点是否在多边形内
  const isPointInPolygon = (lon, lat, polygon) => {
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const xi = polygon[i][0], yi = polygon[i][1];
      const xj = polygon[j][0], yj = polygon[j][1];

      if (((yi > lat) !== (yj > lat)) && (lon < (xj - xi) * (lat - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    return inside;
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleMouseMove,
    handleMouseLeave
  }), [handleMouseMove, handleMouseLeave]);

  if (loading) {
    return (
      <group>
        <Html position={[0, 0, 0]} center>
          <div style={{
            color: '#ffffff',
            fontSize: '16px',
            fontFamily: 'Inter, sans-serif',
            textAlign: 'center',
            padding: '20px',
            background: 'rgba(0, 0, 0, 0.5)',
            borderRadius: '8px',
            backdropFilter: 'blur(10px)'
          }}>
            Loading Geographic Data...
          </div>
        </Html>
      </group>
    );
  }

  return (
    <group ref={groupRef} position={[0, 0, 0]} rotation={[0, 0, 0]} scale={[1.2, 1.2, 1.2]}>
      {/* 立体大陆浮雕 */}
      {continentMeshes.map((meshData, index) => (
        <group key={`continent-relief-${index}`}>
          {/* 主体浮雕 */}
          <mesh position={[0, 0, 0]} castShadow receiveShadow>
            <extrudeGeometry args={[meshData.shape, meshData.extrudeSettings]} />
            <meshStandardMaterial
              color={meshData.material.color}
              metalness={meshData.material.metalness}
              roughness={meshData.material.roughness}
              opacity={meshData.material.opacity}
              transparent={meshData.material.transparent}
              emissive={meshData.material.emissive}
              emissiveIntensity={meshData.material.emissiveIntensity}
            />
          </mesh>

          {/* 边缘轮廓线 */}
          <mesh position={[0, 0, 0.002]}>
            <extrudeGeometry args={[meshData.shape, { depth: 0.001, bevelEnabled: false }]} />
            <meshBasicMaterial
              color="#4a9eff"
              transparent={true}
              opacity={0.6}
              wireframe={true}
            />
          </mesh>
        </group>
      ))}

      {/* 国家边界线 */}
      {countryBorders.map((border, index) => (
        <line key={`country-border-${index}`}>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={border.points.length}
              array={new Float32Array(border.points.flatMap(p => [p.x, p.y, p.z]))}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial
            color="#ffffff"
            transparent={true}
            opacity={0.3}
            linewidth={0.5}
          />
        </line>
      ))}

      {/* 隐形国家检测网格 - 用于精确的鼠标悬停检测 */}
      {countryMeshes.map((meshData, index) => (
        <mesh
          key={`country-detector-${index}`}
          position={[0, 0, 0.3]}
          userData={{ countryName: meshData.countryName, isCountryDetector: true }}
        >
          <extrudeGeometry args={[meshData.shape, { depth: 0.05, bevelEnabled: false }]} />
          <meshBasicMaterial
            transparent={true}
            opacity={0}
            visible={true}
            side={THREE.DoubleSide}
          />
        </mesh>
      ))}

      {/* 简化的光照系统 */}
      <ambientLight intensity={0.5} color="#ffffff" />
      <directionalLight
        position={[10, 15, 8]}
        intensity={1.0}
        color="#ffffff"
        castShadow
        shadow-mapSize-width={1024}
        shadow-mapSize-height={1024}
        shadow-camera-far={30}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
      <directionalLight
        position={[-8, 10, -5]}
        intensity={0.4}
        color="#87ceeb"
      />

      {/* 顶部聚光灯 */}
      <spotLight
        position={[0, 20, 0]}
        angle={Math.PI / 4}
        penumbra={0.3}
        intensity={0.6}
        color="#ffffff"
        castShadow
      />

      {/* 增强的悬浮工具提示 */}
      {showTooltip && hoveredCountry && (
        <Html
          position={[0, 0, 0]}
          style={{
            pointerEvents: 'none',
            position: 'fixed',
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
            zIndex: 1000,
            transform: 'translate(0, -100%)'
          }}
        >
          <div style={{
            background: 'rgba(0, 0, 0, 0.9)',
            color: '#ffffff',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: '600',
            boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(12px)',
            border: '1px solid rgba(74, 158, 255, 0.3)',
            whiteSpace: 'nowrap',
            animation: 'tooltipFadeIn 0.15s ease-out',
            maxWidth: '250px',
            textAlign: 'center',
            transition: 'all 0.1s ease-out',
            position: 'relative'
          }}>
            <div style={{
              position: 'absolute',
              top: '-1px',
              left: '-1px',
              right: '-1px',
              bottom: '-1px',
              background: 'linear-gradient(45deg, rgba(74, 158, 255, 0.2), rgba(74, 158, 255, 0.1))',
              borderRadius: '8px',
              zIndex: -1
            }} />
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: 'linear-gradient(45deg, #4a9eff, #74b9ff)',
                boxShadow: '0 0 8px rgba(74, 158, 255, 0.6)'
              }} />
              {getCountryName(hoveredCountry)}
            </div>
          </div>
        </Html>
      )}
    </group>
  );
});

// 地球数据模块容器 - 带交互功能版
function EarthDataSection() {
  const canvasRef = useRef();
  const modernTechEarthRef = useRef();

  // 处理鼠标移动事件
  const handleCanvasMouseMove = useCallback((event) => {
    if (modernTechEarthRef.current && modernTechEarthRef.current.handleMouseMove) {
      modernTechEarthRef.current.handleMouseMove(event);
    }
  }, []);

  // 处理鼠标离开事件
  const handleCanvasMouseLeave = useCallback((event) => {
    if (modernTechEarthRef.current && modernTechEarthRef.current.handleMouseLeave) {
      modernTechEarthRef.current.handleMouseLeave(event);
    }
  }, []);

  return (
    <div className="earth-data-section">
      {/* 地图容器 */}
      <div
        className="global-map-container"
        onMouseMove={handleCanvasMouseMove}
        onMouseLeave={handleCanvasMouseLeave}
      >
        <Canvas
          ref={canvasRef}
          camera={{
            position: [0, 1, 18],
            fov: 45,
            up: [0, 1, 0]
          }}
          style={{ background: '#0a0f1c' }}
          shadows
          gl={{
            antialias: true,
            alpha: false,
            shadowMap: {
              enabled: true,
              type: THREE.PCFSoftShadowMap
            }
          }}
        >
          <Suspense fallback={null}>
            <color attach="background" args={["#0a0f1c"]} />
            <ModernTechEarth ref={modernTechEarthRef} />
            <OrbitControls
              enableZoom={false}
              enablePan={false}
              enableRotate={false}
              autoRotate={false}
              target={[0, 0, 0]}
            />
          </Suspense>
        </Canvas>
      </div>
    </div>
  );
}

// Starry Button Component
const StarryButton = ({ children, onClick, className = '' }) => {
  const canvasRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const particles = useRef([]);
  const animationId = useRef();
  const mousePos = useRef({ x: 0, y: 0 });
  const hoverEffectIntensityRef = useRef(0);

  // Initialize canvas and particles
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    let animationFrameId;

    // Set canvas dimensions
    const updateDimensions = () => {
      const rect = canvas.parentElement.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      // Only update if dimensions changed significantly
      if (Math.abs(dimensions.width - width) > 1 || Math.abs(dimensions.height - height) > 1) {
        setDimensions({ width, height });
        canvas.width = width * 2; // For retina displays
        canvas.height = height * 2;
        canvas.style.width = `${width}px`;
        canvas.style.height = `${height}px`;

        // Initialize particles
        initParticles(width * 2, height * 2);
      }
    };

    // Initialize particles
    const initParticles = (width, height) => {
      // 增加粒子数量以提高密度和精致感
      const particleCount = Math.floor((width * height) / 800); // 调整密度
      particles.current = [];

      for (let i = 0; i < particleCount; i++) {
        particles.current.push({
          x: Math.random() * width,
          y: Math.random() * height,
          // 调整粒子大小范围，使其更小且变化更微妙
          size: Math.random() * 1 + 0.1, // 调整大小范围
          baseX: Math.random() * width,
          baseY: Math.random() * height,
          // 微调密度和速度，使其更柔和
          density: Math.random() * 10 + 2, // 调整密度范围
          velocityX: (Math.random() - 0.5) * 0.1, // 调整初始速度
          velocityY: (Math.random() - 0.5) * 0.1,
          // 调整颜色范围和透明度，使其偏蓝紫色且更透明柔和
          color: `hsla(${Math.random() * 60 + 200}, 80%, ${Math.random() * 30 + 70}%, ${Math.random() * 0.3 + 0.3})` // 调整颜色和透明度
        });
      }
    };

    // Animation loop with requestAnimationFrame timestamp
    let lastTime = 0;
    const animate = (currentTime) => {
      // Calculate delta time for frame-rate independent animation
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      // Use a multiplier to adjust speed based on frame rate
      const frameMultiplier = Math.min(deltaTime / 16, 2); // Cap at 2x speed for very low frame rates

      // 平滑更新悬停效果强度
      if (isHovered) {
        hoverEffectIntensityRef.current = Math.min(1, hoverEffectIntensityRef.current + 0.05 * frameMultiplier);
      } else {
        hoverEffectIntensityRef.current = Math.max(0, hoverEffectIntensityRef.current - 0.05 * frameMultiplier);
      }

      // 清理画布，增加透明度以形成更长的拖尾效果
      ctx.fillStyle = 'rgba(0, 0, 0, 0.1)'; // 增加透明度
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.current.forEach(particle => {
        // Calculate distance from mouse
        const dx = mousePos.current.x * 2 - particle.x;
        const dy = mousePos.current.y * 2 - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // Calculate force direction
        const forceDirectionX = dx / (distance || 1); // Avoid division by zero
        const forceDirectionY = dy / (distance || 1);

        // Calculate force magnitude with smoother falloff, 使用平滑强度
        let force = 0;
        // 增大交互半径，使粒子更早感知鼠标
        const maxDistance = 200; // 增大交互半径
        if (distance < maxDistance) {
          // Smoother force falloff using easeOutCubic
          const normalizedDistance = distance / maxDistance;
          // 增强力的强度，并乘以悬停效果强度
          force = Math.pow(1 - normalizedDistance, 3) * 0.35 * hoverEffectIntensityRef.current; // 增强力的强度和衰减曲线
        }

        // Apply force with velocity for smoother movement
        particle.velocityX = (particle.velocityX * 0.85) + (forceDirectionX * force * particle.density * 0.1); // 调整速度衰减
        particle.velocityY = (particle.velocityY * 0.85) + (forceDirectionY * force * particle.density * 0.1);

        // Add minimal random movement for subtle organic feel
        particle.velocityX += (Math.random() - 0.5) * 0.003; // 大幅降低随机速度
        particle.velocityY += (Math.random() - 0.5) * 0.003; // 大幅降低随机速度

        // Apply friction
        particle.velocityX *= 0.95; // 增加摩擦力
        particle.velocityY *= 0.95; // 增加摩擦力

        // Update position with stronger easing and velocity
        particle.x += ((particle.baseX - particle.x) * 0.03 + particle.velocityX) * frameMultiplier; // 增加回弹速度
        particle.y += ((particle.baseY - particle.y) * 0.03 + particle.velocityY) * frameMultiplier; // 增加回弹速度

        // Keep particles within bounds
        if (particle.x < 0) particle.x = 0;
        if (particle.x > canvas.width) particle.x = canvas.width;
        if (particle.y < 0) particle.y = 0;
        if (particle.y > canvas.height) particle.y = canvas.height;

        // Draw particle with subtle pulsing effect，使用平滑强度控制大小变化
        const pulse = Math.sin(Date.now() * 0.002 + particle.x * 0.05) * 0.2 + 0.8; // 调整脉冲频率和强度
        // 大小变化也乘以悬停效果强度，并确保基础大小
        const size = particle.size * (1 + pulse * 0.5 * hoverEffectIntensityRef.current); // 调整悬停时的放大倍数

        // Create gradient for particle glow
        const gradient = ctx.createRadialGradient(
          particle.x, particle.y, 0,
          particle.x, particle.y, size * 2 // 保持发光半径与粒子大小关联
        );

        // 调整发光和核心的透明度，使用平滑强度
        const alpha = 0.4 + (isHovered ? 0.5 : 0) * hoverEffectIntensityRef.current; // 调整悬停和非悬停时的透明度
        gradient.addColorStop(0, particle.color);
        gradient.addColorStop(1, 'rgba(0,0,0,0)');

        // Draw glow
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, size * 2, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();

        // Draw particle core
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
      });

      animationId.current = requestAnimationFrame(animate);
    };

    // Initial setup
    updateDimensions();
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(canvas.parentElement);

    // Start animation
    animationId.current = requestAnimationFrame(animate);

    // Cleanup
    return () => {
      cancelAnimationFrame(animationId.current);
      resizeObserver.disconnect();
    };
  }, [dimensions, isHovered]);

  // Handle mouse move
  const handleMouseMove = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    mousePos.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };
  };

  return (
    <button
      className={`starry-button ${className}`}
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseMove={handleMouseMove}
    >
      <canvas ref={canvasRef} className="starry-button-canvas" />
      <span className="starry-button-text">{children}</span>
    </button>
  );
};

// 自定义地球组件
function Earth({ scale = 1.5, position = [2.8, 0, 0] }) {
  const earthRef = useRef();
  const cloudsRef = useRef();

  // 加载地球纹理
  const [colorMap, normalMap, specularMap, cloudsMap] = useTexture([
    '/textures/earth/earthmap1k.jpg',
    '/textures/earth/earth_normal_1k.jpg',
    '/textures/earth/earthspec1k.jpg',
    '/textures/earth/earthclouds.png'
  ]);

  // 控制地球和云层旋转
  useFrame(({ clock }) => {
    const elapsedTime = clock.getElapsedTime();
    if (earthRef.current) earthRef.current.rotation.y = elapsedTime * 0.05;
    if (cloudsRef.current) cloudsRef.current.rotation.y = elapsedTime * 0.055;
  });

  return (
    <group position={position} scale={[scale, scale, scale]} rotation={[0, -Math.PI/4, 0]}>
      {/* 地球本体 */}
      <mesh ref={earthRef} receiveShadow>
        <sphereGeometry args={[1, 64, 64]} />
        <meshPhongMaterial
          map={colorMap}
          normalMap={normalMap}
          specularMap={specularMap}
          shininess={25}
          specular={new THREE.Color('#8af')}
          emissive={new THREE.Color('#003366')}
          emissiveIntensity={0.15}
        />
      </mesh>

      {/* 云层 */}
      <mesh ref={cloudsRef} scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={[1, 64, 64]} />
        <meshPhongMaterial
          map={cloudsMap}
          transparent={true}
          opacity={0.35}
          depthWrite={false}
          side={THREE.DoubleSide}
        />
      </mesh>
    </group>
  );
}

// 增强版星空背景
function EnhancedStars() {
  return (
    <>
      {/* 远距离星空 */}
      <Stars
        radius={300}
        depth={100}
        count={5000}
        factor={4}
        saturation={0}
        fade
        speed={0.5}
      />
      {/* 近距离星空，增加密度 */}
      <Stars
        radius={100}
        depth={50}
        count={3000}
        factor={5}
        saturation={0}
        fade
        speed={0.5}
      />
    </>
  );
}

// 加载屏幕组件 - 重新设计的科技感版本
function LoadingScreen() {
  const currentLang = localStorage.getItem('preferredLanguage') || 'en';
  const isEnglish = currentLang === 'en';
  const [progress, setProgress] = useState(0);
  const [loadingPhase, setLoadingPhase] = useState(0);

  // 模拟加载进度
  useEffect(() => {
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 3;
      });
    }, 50);

    const phaseInterval = setInterval(() => {
      setLoadingPhase(prev => (prev + 1) % 3);
    }, 800);

    return () => {
      clearInterval(progressInterval);
      clearInterval(phaseInterval);
    };
  }, []);

  const loadingTexts = isEnglish ?
    ['Initializing Platform', 'Loading Data Layers', 'Establishing Connections'] :
    ['初始化平台', '加载数据层', '建立连接'];

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      overflow: 'hidden',
      zIndex: 9999
    }}>
      {/* 背景动态网格 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: `
          linear-gradient(rgba(0, 229, 255, 0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(0, 229, 255, 0.1) 1px, transparent 1px)
        `,
        backgroundSize: '60px 60px',
        animation: 'gridMove 10s linear infinite',
        opacity: 0.3
      }} />

      {/* 主要加载器容器 */}
      <div style={{
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        zIndex: 10
      }}>
        {/* 中央加载环 */}
        <div style={{
          position: 'relative',
          width: '200px',
          height: '200px',
          marginBottom: '40px'
        }}>
          {/* 外环 */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            border: '2px solid rgba(0, 229, 255, 0.2)',
            borderRadius: '50%',
            animation: 'pulse 2s ease-in-out infinite'
          }} />

          {/* 进度环 */}
          <svg style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            transform: 'rotate(-90deg)'
          }}>
            <circle
              cx="100"
              cy="100"
              r="95"
              fill="none"
              stroke="rgba(0, 229, 255, 0.8)"
              strokeWidth="3"
              strokeLinecap="round"
              strokeDasharray={`${2 * Math.PI * 95}`}
              strokeDashoffset={`${2 * Math.PI * 95 * (1 - progress / 100)}`}
              style={{
                filter: 'drop-shadow(0 0 10px rgba(0, 229, 255, 0.5))',
                transition: 'stroke-dashoffset 0.1s ease'
              }}
            />
          </svg>

          {/* 内部扫描效果 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '120px',
            height: '120px',
            marginTop: '-60px',
            marginLeft: '-60px',
            border: '1px solid rgba(0, 229, 255, 0.4)',
            borderRadius: '50%',
            animation: 'scanPulse 1.5s ease-in-out infinite'
          }} />

          {/* 中心点 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '8px',
            height: '8px',
            marginTop: '-4px',
            marginLeft: '-4px',
            backgroundColor: '#00e5ff',
            borderRadius: '50%',
            boxShadow: '0 0 20px rgba(0, 229, 255, 0.8)',
            animation: 'centerGlow 1s ease-in-out infinite alternate'
          }} />

          {/* 进度百分比 */}
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            color: '#ffffff',
            fontSize: '24px',
            fontWeight: '300',
            fontFamily: 'Orbitron, monospace'
          }}>
            {Math.round(progress)}%
          </div>
        </div>

        {/* 加载文本 */}
        <div style={{
          textAlign: 'center',
          marginBottom: '30px'
        }}>
          <h2 style={{
            color: '#ffffff',
            fontSize: '24px',
            fontWeight: '300',
            fontFamily: 'Orbitron, sans-serif',
            margin: '0 0 10px 0',
            letterSpacing: '2px'
          }}>
            {loadingTexts[loadingPhase]}
            <span style={{
              animation: 'blink 1s ease-in-out infinite'
            }}>
              {loadingPhase === 0 ? '...' : loadingPhase === 1 ? '..' : '.'}
            </span>
          </h2>

          <div style={{
            color: 'rgba(255, 255, 255, 0.6)',
            fontSize: '14px',
            fontFamily: 'Roboto, sans-serif',
            letterSpacing: '1px'
          }}>
            {isEnglish ? 'INDUSTRIAL DISCOVERY SYSTEM' : '工业探索系统'}
          </div>
        </div>

        {/* 现代化进度条 */}
        <div style={{
          width: '300px',
          height: '4px',
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '2px',
          overflow: 'hidden',
          position: 'relative'
        }}>
          <div style={{
            width: `${progress}%`,
            height: '100%',
            background: 'linear-gradient(90deg, #00e5ff, #0099cc, #00e5ff)',
            borderRadius: '2px',
            transition: 'width 0.1s ease',
            boxShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
            position: 'relative'
          }}>
            {/* 移动的光点 */}
            <div style={{
              position: 'absolute',
              right: '-10px',
              top: '-6px',
              width: '16px',
              height: '16px',
              backgroundColor: '#00e5ff',
              borderRadius: '50%',
              boxShadow: '0 0 15px rgba(0, 229, 255, 0.8)',
              animation: 'glowPulse 1s ease-in-out infinite'
            }} />
          </div>
        </div>

        {/* 状态指示器 */}
        <div style={{
          marginTop: '20px',
          display: 'flex',
          gap: '10px',
          alignItems: 'center'
        }}>
          <div style={{
            width: '8px',
            height: '8px',
            backgroundColor: '#00ff88',
            borderRadius: '50%',
            animation: 'statusBlink 1.5s ease-in-out infinite'
          }} />
          <span style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '12px',
            fontFamily: 'Roboto, sans-serif'
          }}>
            {isEnglish ? 'System Status: Active' : '系统状态：活跃'}
          </span>
        </div>
      </div>

      {/* 四角装饰线 */}
      {['top-left', 'top-right', 'bottom-left', 'bottom-right'].map(position => (
        <div
          key={position}
          style={{
            position: 'absolute',
            ...(position.includes('top') ? {top: '30px'} : {bottom: '30px'}),
            ...(position.includes('left') ? {left: '30px'} : {right: '30px'}),
            width: '60px',
            height: '60px',
            borderColor: 'rgba(0, 229, 255, 0.4)',
            borderStyle: 'solid',
            borderWidth: position.includes('top') && position.includes('left') ? '2px 0 0 2px' :
                         position.includes('top') && position.includes('right') ? '2px 2px 0 0' :
                         position.includes('bottom') && position.includes('left') ? '0 0 2px 2px' :
                         '0 2px 2px 0',
            animation: 'cornerGlow 3s ease-in-out infinite'
          }}
        />
      ))}

      {/* CSS动画样式 */}
      <style jsx>{`
        @keyframes gridMove {
          0% { transform: translate(0, 0); }
          100% { transform: translate(60px, 60px); }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); opacity: 0.8; }
          50% { transform: scale(1.05); opacity: 1; }
        }

        @keyframes scanPulse {
          0%, 100% { transform: scale(1); opacity: 0.6; }
          50% { transform: scale(1.1); opacity: 1; }
        }

        @keyframes centerGlow {
          0% { box-shadow: 0 0 20px rgba(0, 229, 255, 0.8); }
          100% { box-shadow: 0 0 30px rgba(0, 229, 255, 1), 0 0 40px rgba(0, 229, 255, 0.6); }
        }

        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }

        @keyframes glowPulse {
          0%, 100% { transform: scale(1); box-shadow: 0 0 15px rgba(0, 229, 255, 0.8); }
          50% { transform: scale(1.2); box-shadow: 0 0 25px rgba(0, 229, 255, 1); }
        }

        @keyframes statusBlink {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.3; }
        }

        @keyframes cornerGlow {
          0%, 100% { opacity: 0.4; filter: brightness(1); }
          50% { opacity: 1; filter: brightness(1.5); }
        }
      `}</style>
    </div>
  );
}

// 团队成员组件
function TeamMember({ member, isActive, onNext, onPrev }) {
  return (
    <div className={`team-member-slide ${isActive ? 'active' : ''}`}>
      <div className="team-member-content">
        <div className="member-info">
          <div className={`member-role ${isActive ? 'animate-text' : ''}`}>{member.role}</div>
          <h3 className={`member-name ${isActive ? 'animate-text' : ''}`}>{member.name}</h3>
          <div className={`member-social ${isActive ? 'animate-fade-in' : ''}`}>
            {member.twitter && (
              <a href={member.twitter} target="_blank" rel="noopener noreferrer" className="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M23 3.01006C23 3.01006 20.9821 4.20217 19.86 4.54006C19.2577 3.84757 18.4573 3.35675 17.567 3.13398C16.6767 2.91122 15.7395 2.96725 14.8821 3.29451C14.0247 3.62177 13.2884 4.20446 12.773 4.96377C12.2575 5.72309 11.9877 6.62239 12 7.54006V8.54006C10.2426 8.58562 8.50127 8.19587 6.93101 7.4055C5.36074 6.61513 4.01032 5.44869 3 4.01006C3 4.01006 -1 13.0101 8 17.0101C5.94053 18.408 3.48716 19.109 1 19.0101C10 24.0101 21 19.0101 21 7.51006C20.9991 7.23151 20.9723 6.95365 20.92 6.68006C21.9406 5.67355 23 3.01006 23 3.01006Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            )}
            {member.linkedin && (
              <a href={member.linkedin} target="_blank" rel="noopener noreferrer" className="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M6 9H2V21H6V9Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
            )}
          </div>
        </div>
        <div className="member-photo-container">
          <img src={member.image} alt={member.name} className="member-photo" />
        </div>
      </div>
      <div className="team-navigation">
        <button className="nav-button prev-button" onClick={onPrev} aria-label="Previous team member">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        <button className="nav-button next-button" onClick={onNext} aria-label="Next team member">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
}

// 粒子地球组件
function ParticleEarth({ scale = 2, position = [0, 0, 0], color = '#ffffff' }) {
  const pointsRef = useRef();
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2(1, 1)); // 初始化在视图外
  const [hovered, setHovered] = useState(false);
  const numPoints = 25000; // 增加粒子数量
  const earthTexture = useTexture('/textures/earth/earthmap1k.jpg');
  // 添加用于粒子交互的原始尺寸记录
  const pointSizesRef = useRef();
  // 创建一个屏幕鼠标位置状态
  const [pointerPosition, setPointerPosition] = useState([0, 0]);
  // 添加连接点脉冲状态
  const [pulseFactor, setPulseFactor] = useState(0);

  const [particlePositions, particleColors] = useMemo(() => {
    const positions = new Float32Array(numPoints * 3);
    const colors = new Float32Array(numPoints * 3);
    const tempCanvas = document.createElement('canvas');
    const ctx = tempCanvas.getContext('2d');
    const radius = 1;

    // 当纹理加载完成时
    if (earthTexture && earthTexture.image) {
      tempCanvas.width = earthTexture.image.width;
      tempCanvas.height = earthTexture.image.height;
      ctx.drawImage(earthTexture.image, 0, 0);

      const imageData = ctx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
      const data = imageData.data;

      let particleCount = 0;

      // 使用纹理数据创建点的分布
      for (let i = 0; particleCount < numPoints && i < numPoints * 4; i++) {
        // 生成随机球体上的点
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        const x = radius * Math.sin(phi) * Math.cos(theta);
        const y = radius * Math.sin(phi) * Math.sin(theta);
        const z = radius * Math.cos(phi);

        // 计算UV坐标以采样纹理
        const u = 0.5 + Math.atan2(x, z) / (2 * Math.PI);
        const v = 0.5 - Math.asin(y) / Math.PI;

        const col = Math.floor(u * tempCanvas.width);
        const row = Math.floor(v * tempCanvas.height);

        const pixelIndex = (row * tempCanvas.width + col) * 4;
        const brightness = (data[pixelIndex] + data[pixelIndex + 1] + data[pixelIndex + 2]) / 3;

        // 根据亮度决定是否放置粒子
        const threshold = 80; // 阈值调整
        if (brightness > threshold || Math.random() > 0.97) {
          positions[particleCount * 3] = x;
          positions[particleCount * 3 + 1] = y;
          positions[particleCount * 3 + 2] = z;

          // 海洋区域为蓝色，陆地区域为白色
          if (brightness <= threshold) {
            colors[particleCount * 3] = 0.1;
            colors[particleCount * 3 + 1] = 0.5;
            colors[particleCount * 3 + 2] = 1.0;
          } else {
            colors[particleCount * 3] = 1.0;
            colors[particleCount * 3 + 1] = 1.0;
            colors[particleCount * 3 + 2] = 1.0;
          }

          particleCount++;
        }
      }

      // 如果没有生成足够的粒子，填充剩余的
      while (particleCount < numPoints) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[particleCount * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[particleCount * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[particleCount * 3 + 2] = radius * Math.cos(phi);

        colors[particleCount * 3] = 0.8;     // R
        colors[particleCount * 3 + 1] = 0.9; // G
        colors[particleCount * 3 + 2] = 1.0; // B

        particleCount++;
      }
    } else {
      // 如果纹理尚未加载，创建均匀分布的点
      for (let i = 0; i < numPoints; i++) {
        const phi = Math.acos(-1 + 2 * Math.random());
        const theta = Math.random() * Math.PI * 2;

        positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);
        positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
        positions[i * 3 + 2] = radius * Math.cos(phi);

        colors[i * 3] = 0.8;     // R
        colors[i * 3 + 1] = 0.9; // G
        colors[i * 3 + 2] = 1.0; // B
      }
    }

    return [positions, colors];
  }, [numPoints, earthTexture]);

  // 初始化粒子大小数组以便后续操作
  useEffect(() => {
    if (pointsRef.current) {
      // 创建一个新的Float32Array来存储每个粒子的原始大小
      const particleSizes = new Float32Array(numPoints);
      for (let i = 0; i < numPoints; i++) {
        particleSizes[i] = 0.02; // 默认大小改为更小的0.02
      }

      // 将大小数组添加为缓冲区属性
      const geometry = pointsRef.current.geometry;
      geometry.setAttribute('size', new THREE.BufferAttribute(particleSizes, 1));

      // 保存引用以便后续更新
      pointSizesRef.current = geometry.attributes.size;
    }
  }, [numPoints]);

  // 用于监听全局鼠标事件
  useEffect(() => {
    // 创建全局鼠标移动事件监听器
    const handleGlobalMouseMove = (event) => {
      // 获取画布元素
      const canvas = document.querySelector('.holographic-earth-container canvas');
      if (!canvas) return;

      // 计算鼠标相对于画布的正规化坐标(-1至1)
      const rect = canvas.getBoundingClientRect();
      const x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      const y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // 检查鼠标是否在画布内
      if (
        event.clientX >= rect.left &&
        event.clientX <= rect.right &&
        event.clientY >= rect.top &&
        event.clientY <= rect.bottom
      ) {
        mouseRef.current.set(x, y);
        setHovered(true);
        setPointerPosition([event.clientX - rect.left, event.clientY - rect.top]);
      } else {
        setHovered(false);
      }
    };

    // 全局鼠标离开事件
    const handleGlobalMouseLeave = () => {
      setHovered(false);
    };

    // 添加全局事件监听器
    window.addEventListener('mousemove', handleGlobalMouseMove);
    window.addEventListener('mouseleave', handleGlobalMouseLeave);

    // 清理事件监听器
    return () => {
      window.removeEventListener('mousemove', handleGlobalMouseMove);
      window.removeEventListener('mouseleave', handleGlobalMouseLeave);
    };
  }, []);

  // 旋转地球并处理鼠标交互
  useFrame(({ clock, camera }) => {
    const elapsedTime = clock.getElapsedTime();
    if (pointsRef.current) {
      // 地球旋转
      pointsRef.current.rotation.y = elapsedTime * 0.05;

      // 脉冲因子更新
      setPulseFactor(Math.sin(elapsedTime * 2) * 0.5 + 0.5);

      // 当鼠标悬停在画布上时
      if (hovered && pointSizesRef.current) {
        // 设置射线起点和方向
        raycasterRef.current.setFromCamera(mouseRef.current, camera);

        // 重置所有粒子大小
        const sizes = pointSizesRef.current.array;
        for (let i = 0; i < sizes.length; i++) {
          // 逐渐恢复到原始大小，使用0.02作为基础大小
          sizes[i] = Math.max(0.02, sizes[i] * 0.95);
        }

        // 计算与粒子的交点
        const intersects = raycasterRef.current.intersectObject(pointsRef.current, true);

        // 处理相交的粒子
        if (intersects.length > 0) {
          // 获取最近交点的位置
          const intersectPoint = intersects[0].point;

          // 影响半径(相对于地球大小)，减小影响范围
          const radius = 0.8;

          // 遍历所有粒子，检查哪些在影响半径内
          const positions = pointsRef.current.geometry.attributes.position.array;

          for (let i = 0; i < numPoints; i++) {
            // 获取粒子位置
            const px = positions[i * 3];
            const py = positions[i * 3 + 1];
            const pz = positions[i * 3 + 2];

            // 将粒子位置转换为世界坐标
            const particlePos = new THREE.Vector3(px, py, pz).applyMatrix4(pointsRef.current.matrixWorld);

            // 计算粒子到交点的距离
            const distance = particlePos.distanceTo(intersectPoint);

            // 如果在影响半径内，增加大小
            if (distance < radius) {
              // 计算大小增加因子 (越近越大)
              const sizeFactor = 1 - (distance / radius);
              // 设置新大小 (最大是原始大小的3倍)
              sizes[i] = 0.02 * (1 + sizeFactor * 2);
            }
          }

          // 标记属性需要更新
          pointSizesRef.current.needsUpdate = true;
        }
      }
    }
  });

  // 创建连接线的数据
  const connectionLines = useMemo(() => {
    // 四条线的顶点数据 - 再次精确调整到大陆位置的坐标
    const positions = [
      // 线1: 中心点到北美洲 (右上)
      0, 0, 0, -0.4, 0.6, 0.7,
      // 线2: 中心点到欧洲 (中间靠右)
      0, 0, 0, 0.7, 0.5, 0.5,
      // 线3: 中心点到亚洲 (中间)
      0, 0, 0, 0.9, -0.2, 0.4,
      // 线4: 中心点到南美洲/非洲 (左下)
      0, 0, 0, -0.6, -0.7, 0.2
    ];

    return new Float32Array(positions);
  }, []);

  return (
    <group position={position} scale={[scale, scale, scale]} rotation={[0, 0, 0]}>
      <points ref={pointsRef}>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            count={numPoints}
            array={particlePositions}
            itemSize={3}
          />
          <bufferAttribute
            attach="attributes-color"
            count={numPoints}
            array={particleColors}
            itemSize={3}
          />
        </bufferGeometry>
        <pointsMaterial
          vertexColors
          transparent={true}
          opacity={0.8}
          sizeAttenuation={true}
          depthWrite={false}
          onBeforeCompile={(shader) => {
            shader.vertexShader = shader.vertexShader.replace(
              'uniform float size;',
              'attribute float size;'
            );
          }}
        />
      </points>

      {/* 添加3D蓝色连接点 - 再次精确调整位置到大陆上 */}
      {/* 北美洲连接点 */}
      <group position={[-0.4, 0.6, 0.7]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.4, 1.8 + pulseFactor * 0.4, 1.8 + pulseFactor * 0.4]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 欧洲连接点 */}
      <group position={[0.7, 0.5, 0.5]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.5, 1.8 + pulseFactor * 0.5, 1.8 + pulseFactor * 0.5]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 亚洲连接点 */}
      <group position={[0.9, -0.2, 0.4]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.3, 1.8 + pulseFactor * 0.3, 1.8 + pulseFactor * 0.3]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 南美洲/非洲连接点 */}
      <group position={[-0.6, -0.7, 0.2]}>
        <mesh>
          <sphereGeometry args={[0.015, 16, 16]} />
          <meshBasicMaterial color="#00e5ff" transparent opacity={0.9} />
        </mesh>
        <mesh scale={[1.8 + pulseFactor * 0.6, 1.8 + pulseFactor * 0.6, 1.8 + pulseFactor * 0.6]}>
          <sphereGeometry args={[0.01, 16, 16]} />
          <meshBasicMaterial color="#00a5ff" transparent opacity={0.4 - pulseFactor * 0.2} />
        </mesh>
        <pointLight intensity={0.3 + pulseFactor * 0.2} distance={0.3} color="#00e5ff" />
      </group>

      {/* 添加连接线 - 使用渐变色和脉冲效果 */}
      <lineSegments>
        <bufferGeometry>
          <bufferAttribute
            attach="attributes-position"
            array={connectionLines}
            count={8}
            itemSize={3}
          />
        </bufferGeometry>
        <lineBasicMaterial color="#00c8ff" transparent opacity={0.6 - pulseFactor * 0.2} />
      </lineSegments>

      {/* 添加发光效果 */}
      <mesh scale={[1.01, 1.01, 1.01]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#0a4677"
          transparent={true}
          opacity={0.05}
          side={THREE.BackSide}
        />
      </mesh>
      <mesh scale={[1.05, 1.05, 1.05]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#4080ff"
          transparent={true}
          opacity={0.02}
          side={THREE.BackSide}
        />
      </mesh>
      <mesh scale={[1.1, 1.1, 1.1]}>
        <sphereGeometry args={[1, 32, 32]} />
        <meshBasicMaterial
          color="#00c8ff"
          transparent={true}
          opacity={0.01}
          side={THREE.BackSide}
        />
      </mesh>
    </group>
  );
}

// 修改照片展示部分代码
const TeamCarousel = forwardRef(({ teamMembers, activeTeamMember, onNext, onPrev }, ref) => {
  const containerRef = useRef(null);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState(null);
  // 冻结过渡期间的状态
  const [transitionState, setTransitionState] = useState({
    activeIndex: 0,
    renderedCards: []
  });

  // 准备渲染状态
  useEffect(() => {
    if (!isTransitioning) {
      // 只在非过渡状态更新渲染数据
      const renderedCards = [];
      const positions = [-2, -1, 0, 1, 2];

      positions.forEach(position => {
        const totalMembers = teamMembers.length;
        let realIndex = (activeTeamMember + position) % totalMembers;
        if (realIndex < 0) realIndex = totalMembers + realIndex;

        renderedCards.push({
          position,
          member: teamMembers[realIndex],
          index: realIndex,
          className: getPositionClassName(position)
        });
      });

      setTransitionState({
        activeIndex: activeTeamMember,
        renderedCards
      });
    }
  }, [activeTeamMember, isTransitioning, teamMembers]);

  // 获取位置对应的CSS类名
  const getPositionClassName = (position) => {
    if (position === 0) return 'active';
    if (position === 1) return 'next';
    if (position === 2) return 'far-next';
    if (position === -1) return 'prev';
    if (position === -2) return 'far-prev';
    return '';
  };

  // 监听过渡状态
  useEffect(() => {
    if (direction !== null) {
      setIsTransitioning(true);

      // 在过渡动画完成后完成切换
      const timer = setTimeout(() => {
        setIsTransitioning(false);
        setDirection(null);
      }, 400); // 等待过渡完成

      return () => clearTimeout(timer);
    }
  }, [direction]);

  // 处理导航
  const handleNext = () => {
    if (isTransitioning) return; // 防止过渡期间重复触发

    // 修改为"right"，表示向左滑动以显示右侧的下一张照片
    setDirection('right');

    // 延迟调用父组件的onNext，确保动画先启动
    setTimeout(() => {
      onNext();
    }, 400); // 与动画时间同步
  };

  const handlePrev = () => {
    if (isTransitioning) return; // 防止过渡期间重复触发

    // 修改为"left"，表示向右滑动以显示左侧的上一张照片
    setDirection('left');

    // 延迟调用父组件的onPrev，确保动画先启动
    setTimeout(() => {
      onPrev();
    }, 400); // 与动画时间同步
  };

  // 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    handleNext,
    handlePrev
  }));

  return (
    <div
      className={`photos-container ${isTransitioning ? `transitioning ${direction}` : ''}`}
      ref={containerRef}
    >
      {/* 渲染所有照片卡片 */}
      {transitionState.renderedCards.map(card => (
        <div
          key={`team-card-${card.index}-${card.position}`}
          className={`team-photo-card ${card.className}`}
          style={{
            transitionDelay: `${Math.abs(card.position) * 0.03}s` // 减少延迟时间，使切换更直接
          }}
        >
          <img src={card.member.image} alt={card.member.name} className="member-photo" />

          {/* 成员信息 */}
          {card.position === 0 && (
            <div className="member-info-content">
              <div className="member-role">{card.member.role}</div>
              <h3 className="member-name">{card.member.name}</h3>
              <div className="member-social">
                {card.member.twitter && (
                  <a href={card.member.twitter} target="_blank" rel="noopener noreferrer" className="social-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M23 3.01006C23 3.01006 20.9821 4.20217 19.86 4.54006C19.2577 3.84757 18.4573 3.35675 17.567 3.13398C16.6767 2.91122 15.7395 2.96725 14.8821 3.29451C14.0247 3.62177 13.2884 4.20446 12.773 4.96377C12.2575 5.72309 11.9877 6.62239 12 7.54006V8.54006C10.2426 8.58562 8.50127 8.19587 6.93101 7.4055C5.36074 6.61513 4.01032 5.44869 3 4.01006C3 4.01006 -1 13.0101 8 17.0101C5.94053 18.408 3.48716 19.109 1 19.0101C10 24.0101 21 19.0101 21 7.51006C20.9991 7.23151 20.9723 6.95365 20.92 6.68006C21.9406 5.67355 23 3.01006 23 3.01006Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                )}
                {card.member.linkedin && (
                  <a href={card.member.linkedin} target="_blank" rel="noopener noreferrer" className="social-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 8C17.5913 8 19.1174 8.63214 20.2426 9.75736C21.3679 10.8826 22 12.4087 22 14V21H18V14C18 13.4696 17.7893 12.9609 17.4142 12.5858C17.0391 12.2107 16.5304 12 16 12C15.4696 12 14.9609 12.2107 14.5858 12.5858C14.2107 12.9609 14 13.4696 14 14V21H10V14C10 12.4087 10.6321 10.8826 11.7574 9.75736C12.8826 8.63214 14.4087 8 16 8Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M6 9H2V21H6V9Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M4 6C5.10457 6 6 5.10457 6 4C6 2.89543 5.10457 2 4 2C2.89543 2 2 2.89543 2 4C2 5.10457 2.89543 6 4 6Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </a>
                )}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
});

// Floating Navigation Bar Component
function FloatingNavigation() {
  const navigate = useNavigate(); // 添加useNavigate hook
  const [currentLang, setCurrentLang] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const isEnglish = currentLang === 'en';
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否在导航栏外部
      if (!event.target.closest('.floating-navigation')) {
        setIsServicesOpen(false);
        setIsLanguageOpen(false);
      }
    };

    const handleEscapeKey = (event) => {
      if (event.key === 'Escape') {
        setIsServicesOpen(false);
        setIsLanguageOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscapeKey);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, []);

  const handleLanguageChange = (lang) => {
    // 防止重复设置相同语言
    if (currentLang === lang) {
      setIsLanguageOpen(false);
      return;
    }

    // 立即更新状态和关闭下拉菜单
    setCurrentLang(lang);
    setIsLanguageOpen(false);
    localStorage.setItem('preferredLanguage', lang);

    // 使用更平滑的页面刷新方式
    setTimeout(() => {
      window.location.reload();
    }, 200);
  };

  const navigationItems = [
    {
      key: 'home',
      label: isEnglish ? 'Home' : '首页',
      onClick: () => {
        // 滚动到页面顶部，因为这就是Home页面
        window.scrollTo({ top: 0, behavior: 'smooth' });
      },
      isActive: true // 标记当前页面为Home
    },
    {
      key: 'about',
      label: isEnglish ? 'About' : '关于我们',
      onClick: () => {}
    },
    {
      key: 'services',
      label: isEnglish ? 'Services' : '产品服务',
      hasDropdown: true,
      onClick: () => setIsServicesOpen(!isServicesOpen)
    },
    {
      key: 'pricing',
      label: isEnglish ? 'Pricing' : '价格方案',
      onClick: () => {}
    },
    {
      key: 'solution',
      label: isEnglish ? 'Solution' : '解决方案',
      onClick: () => {}
    }
  ];

  return (
    <nav className="floating-navigation">
      <div className="nav-container" data-lang={currentLang}>
        {/* Left side - Navigation items */}
        <div className="nav-left">
          {navigationItems.map((item) => (
            <div key={item.key} className="nav-item-wrapper">
              <button
                className={`nav-item ${item.hasDropdown ? 'has-dropdown' : ''} ${item.isActive ? 'active' : ''}`}
                onClick={item.onClick}
                data-lang={currentLang}
              >
                {item.label}
                {item.hasDropdown && (
                  <svg
                    className={`dropdown-icon ${isServicesOpen ? 'open' : ''}`}
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                  >
                    <path
                      d="M3 4.5L6 7.5L9 4.5"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </button>

              {/* Services Dropdown */}
              {item.key === 'services' && isServicesOpen && (
                <div className="dropdown-menu">
                  <button className="dropdown-item">
                    {isEnglish ? 'Data Analytics' : '数据分析'}
                  </button>
                  <button className="dropdown-item">
                    {isEnglish ? 'Visualization' : '可视化'}
                  </button>
                  <button className="dropdown-item">
                    {isEnglish ? 'Consulting' : '咨询服务'}
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Center - Language selector */}
        <div className="nav-center">
          <div className="language-selector-wrapper">
            <button
              className="language-selector"
              onClick={() => setIsLanguageOpen(!isLanguageOpen)}
              data-lang={currentLang}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="1.5"/>
                <path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke="currentColor" strokeWidth="1.5"/>
              </svg>
              <span>{isEnglish ? 'English' : '中文'}</span>
              <svg
                className={`dropdown-icon ${isLanguageOpen ? 'open' : ''}`}
                width="12"
                height="12"
                viewBox="0 0 12 12"
                fill="none"
              >
                <path
                  d="M3 4.5L6 7.5L9 4.5"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            {/* Language Dropdown */}
            {isLanguageOpen && (
              <div className="dropdown-menu language-dropdown">
                <button
                  className={`dropdown-item ${currentLang === 'en' ? 'active' : ''}`}
                  onClick={() => handleLanguageChange('en')}
                >
                  English
                </button>
                <button
                  className={`dropdown-item ${currentLang === 'zh' ? 'active' : ''}`}
                  onClick={() => handleLanguageChange('zh')}
                >
                  中文
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Login/Register */}
        <div className="nav-right">
          <button
            className="auth-button"
            data-lang={currentLang}
            onClick={() => navigate('/login')}
          >
            {isEnglish ? 'LOGIN / REGISTER' : '登录/注册'}
          </button>
        </div>
      </div>
    </nav>
  );
}

// 主页面组件
export default function WelcomePage() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [activeTeamMember, setActiveTeamMember] = useState(0);
  const teamSectionRef = useRef(null);
  const dataSectionRef = useRef(null);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    company: '',
    industry: ''
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // TeamCarousel的引用
  const teamCarouselRef = useRef(null);

  // 模拟加载过程
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  // 处理进入应用
  const handleEnterApp = () => {
    navigate('/main');
  };

  // 处理滚动到团队部分
  const scrollToTeam = () => {
    teamSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 处理滚动到数据部分
  const scrollToData = () => {
    dataSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 处理表单输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理表单提交
  const handleFormSubmit = (e) => {
    e.preventDefault();
    setSubmitting(true);

    // 模拟提交表单的API调用
    setTimeout(() => {
      console.log('Form submitted:', formData);
      setFormSubmitted(true);
      setSubmitting(false);

      // 5秒后关闭模态框
      setTimeout(() => {
        setShowRequestModal(false);
        setFormSubmitted(false);
        // 重置表单
        setFormData({
          fullName: '',
          email: '',
          company: '',
          industry: ''
        });
      }, 5000);
    }, 1500);
  };

  // 处理申请访问按钮点击
  const handleRequestAccess = () => {
    setShowRequestModal(true);
  };

  // 关闭申请访问模态框
  const closeRequestModal = () => {
    setShowRequestModal(false);
  };

  // 处理联系我们按钮点击 (占位)
  const handleContactUs = () => {
    console.log("Contact Us button clicked");
    // TODO: Implement contact us functionality
  };

  // 获取当前语言
  const currentLang = localStorage.getItem('preferredLanguage') || 'en';
  const isEnglish = currentLang === 'en';

  // 团队成员数据
  const teamMembers = [
    {
      name: isEnglish ? 'Tianqu Zhang' : '张天衢',
      role: isEnglish ? 'CEO & CO FOUNDER' : 'CEO & 联合创始人',
      image: '/images/Tian.jpg',
      twitter: 'https://twitter.com/tianquzhang',
      linkedin: 'https://linkedin.com/in/tianquzhang'
    },
    {
      name: isEnglish ? 'Yuxuan Yang' : '杨宇轩',
      role: isEnglish ? 'CO FOUNDER & CTO' : '联合创始人 & 技术总监',
      image: '/images/Chris.jpg',
      twitter: 'https://twitter.com',
      linkedin: 'https://linkedin.com/in/yuxuanyang'
    },
    {
      name: isEnglish ? 'Anna Wu' : '吴秋筠',
      role: isEnglish ? 'Industrial Policy Director ' : '产业政策总监',
      image: '/images/Anna.jpg',
      twitter: 'https://twitter.com',
      linkedin: 'https://linkedin.com'
    }
  ];

  // 处理团队成员导航 - 仅更新状态
  const nextTeamMember = () => {
    setActiveTeamMember((prev) => (prev + 1) % teamMembers.length);
  };

  const prevTeamMember = () => {
    setActiveTeamMember((prev) => (prev - 1 + teamMembers.length) % teamMembers.length);
  };

  return (
    <div className="welcome-page">
      {loading ? (
        <LoadingScreen />
      ) : (
        <>
          {/* Floating Navigation Bar */}
          <FloatingNavigation />

          {/* 地球容器 - 作为绝对定位的背景 */}
          <div className="earth-container">
            {/* 流星效果 - 放在地球容器内，这样只会出现在星空背景中 */}
            <div className="meteor-container">
              <span className="meteor meteor-1"></span>
              <span className="meteor meteor-2"></span>
              <span className="meteor meteor-3"></span>
              <span className="meteor meteor-4"></span>
            </div>
            <Canvas camera={{ position: [0, 0, 5], fov: 45 }} style={{ background: 'transparent' }}>
              <color attach="background" args={["#010314"]} />
              <ambientLight intensity={1.2} />
              <pointLight position={[10, 5, 10]} intensity={2.2} color="#fff" />
              <directionalLight position={[5, 3, 5]} intensity={2.7} color="#c4f5ff" />
              <Suspense fallback={null}>
                <EnhancedStars />
                <Earth scale={1.6} position={[2.8, 0, 0]} />
                <OrbitControls
                  enableZoom={false}
                  enablePan={false}
                  enableRotate={false}
                  autoRotate={false}
                  target={[0, 0, 0]}
                />
              </Suspense>
            </Canvas>
          </div>

          {/* 内容区域 - 在地球之上 */}
          <div className="content-section">
            <div className="title-section">
              <h1 className="company-name" data-text={isEnglish ? 'Industrial Discovery' : '工业探索'}>
                {isEnglish ? 'Industrial Discovery' : '工业探索'}
              </h1>
              <p className="company-desc">
                {isEnglish
                  ? (
                    <>
                      Industrial Discovery provides a <span className="highlight-text wave">site selection</span> and <span className="highlight-text circle">due diligence</span> platform tailored for industrial real estate and manufacturing investment. By integrating geographic, economic, and infrastructure <span className="highlight-text wave">data</span>, our system enables users to analyze location options, simulate costs, and compare industrial zones with clarity and speed. Designed for both investors and industrial park operators, the platform supports interactive analytics, customizable deployment, and end-to-end decision support to streamline industrial development processes.
                    </>
                  )
                  : (
                    <>
                      工业探索提供专为工业地产和制造业投资定制的<span className="highlight-text wave">选址</span>与<span className="highlight-text circle">尽职调查</span>平台。通过整合地理、经济和基础设施<span className="highlight-text wave">数据</span>，我们的系统使用户能够清晰、快速地分析位置选项、模拟成本并比较工业区。该平台专为投资者和工业园区运营商设计，支持交互式分析、可定制部署和端到端决策支持，以简化工业发展流程。
                    </>
                  )
                }
              </p>
              <div className="button-section">
                <StarryButton
                  className="explore-platform-btn"
                  onClick={handleEnterApp}
                >
                  {isEnglish ? 'EXPLORE PLATFORM' : '探索平台'}
                </StarryButton>
                <div className="scroll-down-container">
                  <button className="scroll-down-btn" onClick={scrollToTeam} aria-label="Scroll to team">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5V19M12 19L5 12M12 19L19 12" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                    <span>{isEnglish ? 'MEET OUR TEAM' : '认识我们的团队'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 新增：地球数据可视化模块 */}
          <EarthDataSection />

          {/* 特性部分 */}
          <div className="features-section">
            <div className="section-transition-top"></div>
            <div className="section-transition-bottom"></div>
            <div className="features-container">
              {/* 特性标题部分 - 改进的科技感标题 */}
              <div className="features-header">
                <div className="tech-title-container">
                  <h2 className="features-title tech-enhanced-title">
                    <span className="tech-title-prefix">&lt; /&gt; </span>
                    <span className="tech-title-letters">
                      {isEnglish ? 'KEY FEATURES' : '核心功能'}
                    </span>
                  </h2>
                </div>
                <p className="features-subtitle">
                  {isEnglish
                    ? 'Our platform offers a comprehensive suite of tools to drive your industrial strategy and decision-making'
                    : '我们的平台提供全面的工具套件，助力您的工业战略和决策制定'
                  }
                </p>



                {/* 添加数字装饰元素 */}
                <div className="tech-digital-decoration">
                  <div className="tech-binary-stream">
                    {[...Array(8)].map((_, i) => (
                      <span key={`binary-${i}`} className={i % 3 === 0 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream right">
                    {[...Array(8)].map((_, i) => (
                      <span key={`binary-right-${i}`} className={i % 4 === 0 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>

                  {/* 新增更多二进制流 */}
                  <div className="tech-binary-stream top-left">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-top-left-${i}`} className={i === 2 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream top-right">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-top-right-${i}`} className={i === 3 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream bottom-left">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-bottom-left-${i}`} className={i === 1 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>
                  <div className="tech-binary-stream bottom-right">
                    {[...Array(6)].map((_, i) => (
                      <span key={`binary-bottom-right-${i}`} className={i === 4 ? "binary-digit highlight" : "binary-digit"}>
                        {Math.round(Math.random())}
                      </span>
                    ))}
                  </div>

                  {/* 添加浮动单个数字 - 增加数量 */}
                  {[...Array(40)].map((_, i) => (
                    <div
                      key={`floating-digit-${i}`}
                      className="floating-digits"
                      style={{
                        left: `${Math.random() * 90 + 5}%`,
                        top: `${Math.random() * 90 + 5}%`,
                        fontSize: `${Math.random() * 12 + 8}px`,
                        opacity: `${Math.random() * 0.5 + 0.3}`,
                        animationDelay: `${Math.random() * 8}s`,
                        animationDuration: `${Math.random() * 10 + 5}s`
                      }}
                    >
                      {Math.round(Math.random())}
                    </div>
                  ))}
                </div>
              </div>

              {/* 添加背景粒子效果 */}
              <div className="features-particles">
                {[...Array(20)].map((_, i) => (
                  <div
                    key={`particle-${i}`}
                    className="feature-particle"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      width: `${Math.random() * 3 + 1}px`,
                      height: `${Math.random() * 3 + 1}px`,
                      animationDelay: `${Math.random() * 4}s`,
                      animationDuration: `${Math.random() * 2 + 3}s`
                    }}
                  />
                ))}
              </div>

              <div className="features-grid">
                {/* Feature 1 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Interactive Mapping' : '交互式地图'}</h3>
                  <p>{isEnglish
                    ? 'Explore industrial parks with our intuitive, interactive map interface.'
                    : '通过直观的交互式地图界面探索工业园区。'}
                  </p>
                </div>

                {/* Feature 2 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Migration Analysis' : '迁移分析'}</h3>
                  <p>{isEnglish
                    ? 'Track and analyze population movements and labor trends.'
                    : '跟踪和分析人口流动及劳动力趋势。'}
                  </p>
                </div>

                {/* Feature 3 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Labor Insights' : '劳动力洞察'}</h3>
                  <p>{isEnglish
                    ? 'Gain valuable insights into workforce demographics and skills.'
                    : '获取有关劳动力人口统计和技能的宝贵见解。'}
                  </p>
                </div>

                {/* Feature 4 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                      <path d="M13.5 7h-3v6h6v-3h-3z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Economic Hotspots' : '经济热点'}</h3>
                  <p>{isEnglish
                    ? 'Identify and analyze economic activity concentrations.'
                    : '识别和分析经济活动集中区域。'}
                  </p>
                </div>

                {/* Feature 5 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95C8.08 7.14 9.94 6 12 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11c1.56.1 2.78 1.41 2.78 2.96 0 1.65-1.35 3-3 3z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Env. Monitoring' : '环境监测'}</h3>
                  <p>{isEnglish
                    ? 'Monitor environmental conditions and sustainability metrics.'
                    : '监测环境条件和可持续性指标。'}
                  </p>
                </div>

                {/* Feature 6 - Data Visualization */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z" fill="currentColor"/>
                      <path d="M3.5 14.5l-1.41-1.5L0 15.5l3.5 3.5 7.5-7.5-1.5-1.5z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Data Visualization' : '数据可视化'}</h3>
                  <p>{isEnglish
                    ? 'Transform complex data into clear, actionable insights.'
                    : '将复杂数据转化为清晰、可操作的见解。'}
                  </p>
                </div>

                {/* Feature 7 - Custom Reports */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Custom Reports' : '定制报告'}</h3>
                  <p>{isEnglish
                    ? 'Generate tailored reports for your specific needs.'
                    : '根据您的具体需求生成定制报告。'}
                  </p>
                </div>

                {/* Feature 8 */}
                <div className="feature-card card-with-border">
                  <div className="feature-icon">
                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.03-6.61l1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.07 4.74 14.12 4 12 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.12-.74-4.07-1.97-5.61zM12 20c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h3>{isEnglish ? 'Real-time Updates' : '实时更新'}</h3>
                  <p>{isEnglish
                    ? 'Stay current with the latest data and trends.'
                    : '随时了解最新数据和趋势。'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 团队部分 - 可滚动到达 */}
          <div className="team-section" ref={teamSectionRef}>
            <div className="team-section-inner">
              <div className="team-left-content">
                <div className="team-heading">
                  <h2 className="team-title">{isEnglish ? 'Our team' : '我们的团队'}</h2>
                  <p className="team-subtitle">{isEnglish ? 'Let technology do the heavy lifting and grow initiatives into tangible results' : 'Let technology do the heavy lifting and grow initiatives into tangible results'}</p>
                </div>
              </div>

              <div className="team-carousel">
                <TeamCarousel
                  ref={teamCarouselRef}
                  teamMembers={teamMembers}
                  activeTeamMember={activeTeamMember}
                  onNext={nextTeamMember}
                  onPrev={prevTeamMember}
                />

                <div className="team-navigation">
                  <button
                    className="nav-button prev-button"
                    onClick={() => {
                      if (teamCarouselRef.current) {
                        teamCarouselRef.current.handlePrev();
                      }
                    }}
                    aria-label="Previous team member"
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M15 18L9 12L15 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                  <button
                    className="nav-button next-button"
                    onClick={() => {
                      if (teamCarouselRef.current) {
                        teamCarouselRef.current.handleNext();
                      }
                    }}
                    aria-label="Next team member"
                  >
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 18L15 12L9 6" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* 全新工业园区定位模块 */}
          <div className="futuristic-industrial-module" ref={dataSectionRef}>
            {/* 背景网格和电路 */}
            <div className="industrial-grid-bg"></div>
            <div className="circuit-pattern"></div>

            {/* 内容容器 */}
            <div className="industrial-module-container">
              {/* 地球区域 */}
              <div className="holographic-earth-container">
                {/* 扫描环绕效果 */}
                <div className="earth-scan-ring"></div>
                <div className="earth-scan-ring"></div>
                <div className="earth-scan-ring"></div>

                {/* 地球组件 - 保持原有的粒子地球效果 */}
                <Canvas>
                  <Suspense fallback={null}>
                    <ambientLight intensity={0.5} />
                    <pointLight position={[10, 10, 10]} intensity={0.8} />
                    <ParticleEarth scale={2.2} position={[0, 0, 0]} color="#00a8ff" />
                    <OrbitControls
                      enableZoom={false}
                      enablePan={false}
                      enableRotate={false}
                      autoRotate={false}
                    />
                  </Suspense>
                </Canvas>
              </div>

              {/* 指挥中心面板 */}
              <div className="command-center-container">
                <div className="command-center-frame">
                  {/* 角落装饰 */}
                  <div className="corner-accent top-left-corner"></div>
                  <div className="corner-accent top-right-corner"></div>
                  <div className="corner-accent bottom-left-corner"></div>
                  <div className="corner-accent bottom-right-corner"></div>

                  {/* 数据节点背景效果 */}
                  <div className="data-nodes">
                    <div className="data-node node-1"></div>
                    <div className="data-node node-2"></div>
                    <div className="data-node node-3"></div>
                    <div className="data-node node-4"></div>
                  </div>

                  {/* 内容区域 */}
                  <div className="command-center-content">
                    {/* 标题区域 */}
                    <div className="industrial-title-container">
                      <h2 className="industrial-title">
                        <span className="industrial-title-line">
                          <span className="industrial-title-word">POSITION</span>
                        </span>
                        <span className="industrial-title-line">
                          <span className="industrial-title-word">YOUR</span>
                          <span className="industrial-title-word industrial-title-highlight">INDUSTRIAL</span>
                        </span>
                        <span className="industrial-title-line">
                          <span className="industrial-title-word">PARK</span>
                          <span className="industrial-title-word">FOR</span>
                          <span className="industrial-title-word industrial-title-highlight">SUCCESS</span>
                        </span>
                      </h2>
                    </div>

                    {/* 描述文本 */}
                    <p className="industrial-description">
                      {isEnglish
                        ? 'Join forces with us on the Industrial Geo Dev system for unparalleled insights and opportunities.'
                        : '加入我们的工业地理开发系统，获取无与伦比的洞察力和机会。'
                      }
                    </p>
                    <p className="industrial-sub-description">
                      {isEnglish
                        ? 'Empower your site selection, cost simulation, and industrial zone comparison with interactive analytics and real-time data.'
                        : '用交互式分析和实时数据，助力您的选址、成本模拟与园区对比。'}
                    </p>

                    {/* 行动按钮 */}
                    <div className="command-center-cta">
                      <StarryButton className="industrial-cta-button" onClick={handleContactUs}>
                        {isEnglish ? 'CONTACT US' : '联系我们'}
                      </StarryButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 申请访问模态框 */}
          {showRequestModal && (
            <div className="request-modal-overlay" onClick={closeRequestModal}>
              <div className="request-modal" onClick={e => e.stopPropagation()}>
                <button className="close-modal-btn" onClick={closeRequestModal}>×</button>
                {formSubmitted ? (
                  <div className="form-success">
                    <div className="success-icon">✓</div>
                    <h3 className="modal-title">{isEnglish ? 'Request Sent!' : '申请已发送！'}</h3>
                    <p className="modal-description">
                      {isEnglish
                        ? 'Thank you for your interest. We will contact you soon with access details.'
                        : '感谢您的兴趣。我们将很快与您联系并提供访问详情。'
                      }
                    </p>
                  </div>
                ) : (
                  <>
                    <h3 className="modal-title">{isEnglish ? 'Request Access' : '申请访问'}</h3>
                    <p className="modal-description">
                      {isEnglish
                        ? 'Fill out the form below to request access to Dala, our industrial intelligence system.'
                        : '填写以下表格申请访问Dala，我们的工业智能系统。'
                      }
                    </p>
                    <form className="request-form" onSubmit={handleFormSubmit}>
                      <div className="form-group">
                        <input
                          type="text"
                          name="fullName"
                          value={formData.fullName}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Full Name" : "姓名"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Work Email" : "工作邮箱"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <input
                          type="text"
                          name="company"
                          value={formData.company}
                          onChange={handleInputChange}
                          placeholder={isEnglish ? "Company" : "公司"}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <select
                          className="form-input"
                          name="industry"
                          value={formData.industry}
                          onChange={handleInputChange}
                          required
                        >
                          <option value="" disabled>
                            {isEnglish ? "Industry" : "行业"}
                          </option>
                          <option value="manufacturing">
                            {isEnglish ? "Manufacturing" : "制造业"}
                          </option>
                          <option value="realEstate">
                            {isEnglish ? "Real Estate" : "房地产"}
                          </option>
                          <option value="consulting">
                            {isEnglish ? "Consulting" : "咨询"}
                          </option>
                          <option value="investment">
                            {isEnglish ? "Investment" : "投资"}
                          </option>
                          <option value="other">
                            {isEnglish ? "Other" : "其他"}
                          </option>
                        </select>
                      </div>
                      <button
                        type="submit"
                        className={`submit-request-btn ${submitting ? 'submitting' : ''}`}
                        disabled={submitting}
                      >
                        {submitting
                          ? (isEnglish ? 'SENDING...' : '发送中...')
                          : (isEnglish ? 'SUBMIT REQUEST' : '提交申请')
                        }
                      </button>
                    </form>
                  </>
                )}
              </div>
            </div>
          )}
        </>
      )}
      <footer className="site-footer">
        <div className="footer-container">
          <div className="footer-content">
            {/* 公司信息 */}
            <div className="footer-brand">
              <h3 className="footer-logo">
                <span className="logo-highlight">INDUSTRIAL</span>
                <span className="logo-normal">DISCOVERY</span>
              </h3>
              <p className="footer-tagline">
                {isEnglish ? 'Powering Industrial Intelligence' : '驱动工业智能'}
              </p>
            </div>

            {/* 联系信息 */}
            <div className="footer-contact">
              <span className="contact-email">Email： <EMAIL></span>
              <span className="contact-location">Delaware, United States</span>
            </div>
          </div>

          {/* 底部版权 */}
          <div className="footer-bottom">
            <p>&copy; {new Date().getFullYear()} Industrial Discovery Inc. {isEnglish ? 'All rights reserved.' : '保留所有权利。'}</p>
            <div className="footer-links">
              <a href="#">{isEnglish ? 'Privacy Policy' : '隐私政策'}</a>
              <a href="#">{isEnglish ? 'Terms of Service' : '服务条款'}</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}